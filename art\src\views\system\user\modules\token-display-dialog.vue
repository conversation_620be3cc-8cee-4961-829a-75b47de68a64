<template>
  <ElDialog
    v-model="dialogVisible"
    title="刷新令牌"
    width="60%"
    align-center
    :close-on-click-modal="false"
  >
    <div v-loading="loading" class="token-content">
      <ElForm label-width="100px">
        <ElFormItem label="用户名">
          <ElInput v-model="tokenData.username" readonly disabled />
        </ElFormItem>

        <ElFormItem label="刷新令牌">
          <ElInput
            v-model="tokenData.refresh_token"
            type="textarea"
            :rows="8"
            readonly
            disabled
            placeholder="暂无刷新令牌"
            class="token-textarea"
          />
        </ElFormItem>

        <div class="token-actions">
          <ElButton
            v-if="tokenData.refresh_token && tokenData.refresh_token !== '暂无刷新令牌'"
            type="primary"
            @click="copyToken"
          >
            <ElIcon><CopyDocument /></ElIcon>
            复制令牌
          </ElButton>
        </div>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { UserService } from '@/api/usersApi'
  import { ElMessage } from 'element-plus'
  import { CopyDocument } from '@element-plus/icons-vue'

  interface Props {
    visible: boolean
    userId: number | null
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 弹窗显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 加载状态
  const loading = ref(false)

  // 令牌数据
  const tokenData = ref<{
    username: string
    refresh_token: string
  }>({
    username: '',
    refresh_token: ''
  })

  // 监听弹窗显示状态，加载令牌数据
  watch(
    () => props.visible,
    (visible) => {
      if (visible && props.userId) {
        loadTokenData()
      }
    }
  )

  // 加载令牌数据
  const loadTokenData = async () => {
    if (!props.userId) return

    loading.value = true
    try {
      const response = await UserService.getUserRefreshToken(props.userId)

      // HTTP工具已经提取了data字段，response直接就是令牌数据
      if (response && typeof response === 'object') {
        const data = response as any
        tokenData.value = {
          username: data.username || '',
          refresh_token: data.refresh_token || '暂无刷新令牌'
        }
      } else {
        tokenData.value = {
          username: '',
          refresh_token: '暂无刷新令牌'
        }
      }
    } catch (error: any) {
      console.error('加载令牌失败:', error)
      ElMessage.error(error?.response?.data?.msg || '加载令牌失败')

      // 设置默认值
      tokenData.value = {
        username: '',
        refresh_token: '加载失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 复制令牌到剪贴板
  const copyToken = async () => {
    try {
      await navigator.clipboard.writeText(tokenData.value.refresh_token)
      ElMessage.success('令牌已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请手动复制')
    }
  }
</script>

<style lang="scss" scoped>
  .token-content {
    min-height: 200px;
  }

  .token-textarea {
    :deep(.el-textarea__inner) {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
    }
  }

  .token-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
  }
</style>

<!-- 系统测试页面 -->
<template>
  <div class="test-page">
    <ElCard shadow="never" style="margin-bottom: 16px">
      <template #header>
        <h3>🧪 Dawn ERP 系统测试面板</h3>
      </template>

      <!-- 系统状态 -->
      <div class="status-section">
        <h4>📊 系统状态</h4>
        <ElRow :gutter="16">
          <ElCol :span="6">
            <ElCard>
              <ElStatistic title="后端连接" :value="backendStatus as any" />
              <div style="margin-top: 8px">
                <ElTag :type="backendStatus === '正常' ? 'success' : 'danger'">
                  {{ backendStatus }}
                </ElTag>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard>
              <ElStatistic title="数据库状态" :value="databaseCount" suffix="个" />
              <div style="margin-top: 8px">
                <ElTag type="info">多数据库架构</ElTag>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard>
              <ElStatistic title="当前区域" :value="currentRegion as any" />
              <div style="margin-top: 8px">
                <ElTag type="primary">{{ currentRegionCode }}</ElTag>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard>
              <ElStatistic title="用户角色" :value="userRole as any" />
              <div style="margin-top: 8px">
                <ElTag type="warning">{{ userRoleDisplay }}</ElTag>
              </div>
            </ElCard>
          </ElCol>
        </ElRow>
      </div>

      <!-- 功能测试 -->
      <div class="test-section" style="margin-top: 24px">
        <h4>🔧 功能测试</h4>
        <ElSpace wrap>
          <ElButton type="primary" @click="testUserAPI" :loading="testing.user">
            测试用户API
          </ElButton>
          <ElButton type="success" @click="testBusinessAPI" :loading="testing.business">
            测试业务API
          </ElButton>
          <ElButton type="info" @click="testMenuAPI" :loading="testing.menu">
            测试菜单API
          </ElButton>
          <ElButton type="warning" @click="createTestData" :loading="testing.testData">
            创建测试数据
          </ElButton>
          <ElButton @click="getDatabaseStatus" :loading="testing.database">
            获取数据库状态
          </ElButton>
        </ElSpace>
      </div>

      <!-- 测试结果 -->
      <div class="result-section" style="margin-top: 24px" v-if="testResults.length > 0">
        <h4>📋 测试结果</h4>
        <ElTimeline>
          <ElTimelineItem
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.success ? 'success' : 'danger'"
            :timestamp="result.timestamp"
          >
            <ElCard>
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ result.title }}</span>
                <ElTag :type="result.success ? 'success' : 'danger'">
                  {{ result.success ? '成功' : '失败' }}
                </ElTag>
              </div>
              <div style="margin-top: 8px; color: #666; font-size: 12px">
                {{ result.message }}
              </div>
              <div v-if="result.data" style="margin-top: 8px">
                <ElCollapse>
                  <ElCollapseItem title="详细数据" name="data">
                    <pre
                      style="font-size: 12px; background: #f5f5f5; padding: 8px; border-radius: 4px"
                      >{{ JSON.stringify(result.data, null, 2) }}</pre
                    >
                  </ElCollapseItem>
                </ElCollapse>
              </div>
            </ElCard>
          </ElTimelineItem>
        </ElTimeline>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { UserService } from '@/api/usersApi'
  import { CustomerService, ProductService, TestDataService } from '@/api/businessApi'
  import { menuService } from '@/api/menuApi'
  import { useUserStore } from '@/store/modules/user'
  import { ElMessage } from 'element-plus'

  defineOptions({ name: 'SystemTest' })

  const userStore = useUserStore()

  // 响应式数据
  const backendStatus = ref('检测中...')
  const databaseCount = ref(0)
  const currentRegion = ref('')
  const currentRegionCode = ref('')
  const userRole = ref('')
  const userRoleDisplay = ref('')

  // 测试状态
  const testing = reactive({
    user: false,
    business: false,
    menu: false,
    testData: false,
    database: false
  })

  // 测试结果
  const testResults = ref<
    Array<{
      title: string
      success: boolean
      message: string
      timestamp: string
      data?: any
    }>
  >([])

  // 添加测试结果
  const addTestResult = (title: string, success: boolean, message: string, data?: any) => {
    testResults.value.unshift({
      title,
      success,
      message,
      timestamp: new Date().toLocaleTimeString(),
      data
    })
  }

  // 测试用户API
  const testUserAPI = async () => {
    testing.user = true
    try {
      const userInfo = await UserService.getUserInfo()
      addTestResult('用户API测试', true, '成功获取用户信息', userInfo)
      ElMessage.success('用户API测试成功')
    } catch (error) {
      addTestResult('用户API测试', false, `失败: ${error}`)
      ElMessage.error('用户API测试失败')
    } finally {
      testing.user = false
    }
  }

  // 测试业务API
  const testBusinessAPI = async () => {
    testing.business = true
    try {
      const customers = await CustomerService.getCustomerList({ current: 1, size: 5 })
      const products = await ProductService.getProductList({ current: 1, size: 5 })

      addTestResult(
        '业务API测试',
        true,
        `成功获取 ${customers.total} 个客户, ${products.total} 个产品`,
        {
          customers: customers.records.length,
          products: products.records.length
        }
      )
      ElMessage.success('业务API测试成功')
    } catch (error) {
      addTestResult('业务API测试', false, `失败: ${error}`)
      ElMessage.error('业务API测试失败')
    } finally {
      testing.business = false
    }
  }

  // 测试菜单API
  const testMenuAPI = async () => {
    testing.menu = true
    try {
      const menuData = await menuService.getMenuList()
      addTestResult('菜单API测试', true, `成功获取 ${menuData.menuList.length} 个菜单项`, menuData)
      ElMessage.success('菜单API测试成功')
    } catch (error) {
      addTestResult('菜单API测试', false, `失败: ${error}`)
      ElMessage.error('菜单API测试失败')
    } finally {
      testing.menu = false
    }
  }

  // 创建测试数据
  const createTestData = async () => {
    testing.testData = true
    try {
      const result = await TestDataService.createTestData()
      addTestResult('创建测试数据', true, '成功创建测试数据', result)
      ElMessage.success('测试数据创建成功')
    } catch (error) {
      addTestResult('创建测试数据', false, `失败: ${error}`)
      ElMessage.error('创建测试数据失败')
    } finally {
      testing.testData = false
    }
  }

  // 获取数据库状态
  const getDatabaseStatus = async () => {
    testing.database = true
    try {
      const status = await TestDataService.getDatabaseStatus()
      databaseCount.value = status.databases.length
      addTestResult('数据库状态', true, `获取到 ${status.databases.length} 个数据库状态`, status)
      ElMessage.success('数据库状态获取成功')
    } catch (error) {
      addTestResult('数据库状态', false, `失败: ${error}`)
      ElMessage.error('数据库状态获取失败')
    } finally {
      testing.database = false
    }
  }

  // 初始化
  onMounted(async () => {
    // 获取用户信息
    const userInfo = userStore.getUserInfo
    currentRegion.value = userInfo.regionName || '未知'
    currentRegionCode.value = userInfo.regionCode || 'UNKNOWN'
    userRole.value = userInfo.role || '未知'
    userRoleDisplay.value = userInfo.roleDisplay || '未知'

    // 测试后端连接
    try {
      await UserService.getUserInfo()
      backendStatus.value = '正常'
    } catch {
      backendStatus.value = '异常'
    }

    // 获取数据库状态
    getDatabaseStatus()
  })
</script>

<style lang="scss" scoped>
  .test-page {
    padding: 16px;
  }

  .status-section,
  .test-section,
  .result-section {
    h4 {
      margin-bottom: 16px;
      color: #333;
    }
  }
</style>

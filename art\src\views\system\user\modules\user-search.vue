<template>
  <ArtSearchBar
    v-model:filter="formFilters"
    :items="formItems"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types'

  interface Props {
    modelValue: any
  }

  interface Emits {
    (e: 'update:modelValue', value: any): void
    (e: 'search'): void
    (e: 'reset'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',        // 登录名
    role: '',        // 角色（等级）
    isActive: ''     // 状态
  }

  // 响应式表单数据
  const formFilters = computed({
    get: () => props.modelValue || initialSearchState,
    set: (value) => emit('update:modelValue', value)
  })

  // 重置表单
  const handleReset = () => {
    emit('update:modelValue', { ...initialSearchState })
    emit('reset')
  }

  // 搜索处理
  const handleSearch = () => {
    console.log('搜索参数:', formFilters.value)
    emit('search')
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '登录名',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入登录名'
      },
      onChange: handleFormChange
    },
    {
      label: '等级',
      prop: 'role',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择用户等级'
      },
      options: () => [
        { label: '超级管理员', value: 'SUPER_ADMIN' },
        { label: '区域管理员', value: 'REGION_ADMIN' },
        { label: '普通会员', value: 'MEMBER' },
        { label: '会员助理', value: 'MEMBER_ASSISTANT' },
        { label: '翻译助理', value: 'TRANSLATOR_ASSISTANT' },
        { label: '文档助理', value: 'DOCUMENT_ASSISTANT' },
        { label: '精细化操作人员', value: 'OPERATION_SPECIALIST' },
        { label: '财务人员', value: 'FINANCE_STAFF' },
        { label: '库管', value: 'WAREHOUSE_MANAGER' }
      ],
      onChange: handleFormChange
    },
    {
      label: '状态',
      prop: 'isActive',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: () => [
        { label: '启用', value: 'true' },
        { label: '禁用', value: 'false' }
      ],
      onChange: handleFormChange
    }
  ]
</script>

<template>
  <div class="region-selector">
    <div class="region-buttons">
      <ElButton
        v-for="region in regions"
        :key="region.code"
        :type="selectedRegion === region.code ? 'primary' : 'default'"
        :size="'small'"
        @click="handleRegionChange(region.code)"
        class="region-btn"
      >
        {{ region.name }}
      </ElButton>
    </div>
    <div class="region-info" v-if="selectedRegion && selectedRegion !== 'ALL'">
      <ElTag type="info" size="small"> 当前区域: {{ getRegionName(selectedRegion) }} </ElTag>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElButton, ElTag } from 'element-plus'
  import { computed, ref } from 'vue'

  // 定义组件属性
  interface Props {
    modelValue?: string
  }

  // 定义事件
  interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'change', value: string): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: 'ALL'
  })

  const emit = defineEmits<Emits>()

  // 区域配置
  const regions = ref([
    { code: 'ALL', name: '全部' },
    { code: 'MPR', name: 'MPR' },
    { code: 'RL', name: 'RL' },
    { code: 'EO', name: 'EO' },
    { code: 'ZZ', name: 'ZZ' },
    { code: 'WH', name: 'WH' }
  ])

  // 当前选中的区域
  const selectedRegion = computed({
    get: () => props.modelValue,
    set: (value: string) => {
      emit('update:modelValue', value)
    }
  })

  // 获取区域名称
  const getRegionName = (code: string) => {
    const region = regions.value.find((r) => r.code === code)
    return region ? region.name : code
  }

  // 处理区域切换
  const handleRegionChange = (regionCode: string) => {
    selectedRegion.value = regionCode
    emit('change', regionCode)
  }
</script>

<style scoped>
  .region-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }

  .region-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .region-btn {
    min-width: 60px;
    height: 32px; /* 固定按钮高度 */
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box; /* 确保padding不影响总高度 */
  }

  .region-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 确保选中状态和默认状态高度一致 */
  .region-btn.el-button--primary,
  .region-btn.el-button--default {
    height: 32px !important;
    line-height: 1;
    padding: 8px 15px !important; /* 固定内边距 */
  }

  /* 覆盖Element Plus的默认样式 */
  .region-btn:deep(.el-button) {
    height: 32px !important;
  }

  /* 确保按钮内容垂直居中 */
  .region-btn span {
    line-height: 1;
  }

  /* 选中状态的特殊样式 */
  .region-btn.el-button--primary {
    background: var(--el-color-primary) !important;
    border-color: var(--el-color-primary) !important;
    color: white !important;
    font-weight: 500;
  }

  /* 默认状态的样式 */
  .region-btn.el-button--default {
    background: var(--el-fill-color-light) !important;
    border-color: var(--el-border-color) !important;
    color: var(--el-text-color-regular) !important;
  }

  /* hover状态优化 */
  .region-btn.el-button--default:hover {
    background: var(--el-color-primary-light-9) !important;
    border-color: var(--el-color-primary-light-7) !important;
    color: var(--el-color-primary) !important;
  }

  .region-info {
    display: flex;
    align-items: center;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .region-buttons {
      justify-content: center;
    }

    .region-btn {
      flex: 1;
      min-width: 50px;
    }
  }
</style>

<!-- 用户管理 -->
<!-- art-full-height 自动计算出页面剩余高度 -->
<!-- art-table-card 一个符合系统样式的 class，同时自动撑满剩余高度 -->
<!-- 如果你想使用 template 语法，请移步功能示例下面的高级表格示例 -->
<template>
  <div class="user-page art-full-height">
    <!-- 搜索栏 -->
    <UserSearch
      v-model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 区域选择器 - 仅超级管理员可见 -->
      <RegionSelector
        v-if="isSuperAdmin"
        v-model="selectedRegion"
        @change="handleRegionChange"
      />

      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="refresh">
        <template #left>
          <ElButton @click="showDialog('add')">新增用户</ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        :loading="loading"
        :data="data"
        :columns="columns"
        :pagination="pagination"
        :table-config="{
          rowKey: 'id',
          stripe: true,
          border: true
        }"
        :layout="{ marginTop: 10 }"
        @row:selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
      </ArtTable>

      <!-- 用户弹窗 -->
      <UserDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :user-data="currentUserData"
        @submit="handleDialogSubmit"
      />

      <!-- 权限管理弹窗 -->
      <PermissionManagement
        v-model:visible="permissionDialogVisible"
        :user-id="currentPermissionUserId"
        @success="handlePermissionSuccess"
      />

      <!-- 令牌展示弹窗 -->
      <TokenDisplayDialog
        v-model:visible="tokenDialogVisible"
        :user-id="currentTokenUserId"
      />

      <!-- 地址编辑弹窗 -->
      <AddressEditDialog
        v-model:visible="addressDialogVisible"
        :address-data="currentAddressData"
        :user-id="currentAddressUserId"
        @submit="handleAddressSubmit"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { UserService } from '@/api/usersApi'
import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import { useTable } from '@/composables/useTable'
import { useUserStore } from '@/store/modules/user'
import { ElMessage, ElMessageBox, ElSwitch, ElTag } from 'element-plus'
import AddressEditDialog from './modules/address-edit-dialog.vue'
import PermissionManagement from './modules/permission-management.vue'
import RegionSelector from './modules/region-selector.vue'
import TokenDisplayDialog from './modules/token-display-dialog.vue'
import UserDialog from './modules/user-dialog.vue'
import UserSearch from './modules/user-search.vue'

  defineOptions({ name: 'User' })

  type UserListItem = Api.User.UserListItem
  const { width } = useWindowSize()
  const { getUserList, createUser, updateUser, toggleUserStatus, toggleMWSStatus } = UserService

  // 用户store
  const userStore = useUserStore()
  const userInfo = computed(() => userStore.getUserInfo)

  // 判断是否为超级管理员
  const isSuperAdmin = computed(() => {
    return userInfo.value?.role === 'SUPER_ADMIN'
  })

  // 区域选择相关
  const selectedRegion = ref<string>('ALL')

  // 弹窗相关
  const dialogType = ref<Form.DialogType>('add')
  const dialogVisible = ref(false)
  const currentUserData = ref<Partial<UserListItem>>({})

  // 权限管理弹窗相关
  const permissionDialogVisible = ref(false)
  const currentPermissionUserId = ref<number | null>(null)

  // 令牌展示弹窗相关
  const tokenDialogVisible = ref(false)
  const currentTokenUserId = ref<number | null>(null)

  // 地址编辑弹窗相关
  const addressDialogVisible = ref(false)
  const currentAddressData = ref<any>({})
  const currentAddressUserId = ref<number | null>(null)

  // 选中行
  const selectedRows = ref<UserListItem[]>([])

  /**
   * 处理区域切换
   */
  const handleRegionChange = (regionCode: string) => {
    selectedRegion.value = regionCode
    // 更新搜索参数，添加区域过滤
    if (regionCode !== 'ALL') {
      searchParams.region = regionCode
    } else {
      delete searchParams.region
    }
    // 重新获取数据
    getDataByPage()
  }

  /**
   * 处理账户状态切换
   */
  const handleAccountToggle = async (row: UserListItem, newStatus: boolean): Promise<void> => {
    try {
      await ElMessageBox.confirm(
        `确定要${newStatus ? '启用' : '禁用'}用户 ${row.userName} 的账户吗？`,
        '切换账户状态',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: newStatus ? 'warning' : 'error'
        }
      )

      // 调用切换状态API
      await toggleUserStatus(row.id, newStatus)

      // 更新本地数据
      row.isActive = newStatus

      ElMessage.success(`用户账户已${newStatus ? '启用' : '禁用'}`)

      // 刷新数据以确保同步
      await refresh()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('切换账户状态失败:', error)
        ElMessage.error(error?.response?.data?.msg || '切换账户状态失败')
      }
    }
  }

  /**
   * 处理MWS开关状态切换
   */
  const handleMWSToggle = async (row: UserListItem, newStatus: boolean): Promise<void> => {
    try {
      await ElMessageBox.confirm(
        `确定要${newStatus ? '启用' : '禁用'}用户 ${row.userName} 的MWS功能吗？`,
        '切换MWS状态',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

             // 调用MWS状态切换API
       await toggleMWSStatus(row.id, newStatus)

      // 更新本地数据
      row.mwsSwitch = newStatus

      ElMessage.success(`MWS功能已${newStatus ? '启用' : '禁用'}`)

      // 刷新数据以确保同步
      await refresh()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('切换MWS状态失败:', error)
        ElMessage.error(error?.response?.data?.msg || '切换MWS状态失败')
      }
    }
  }

  const {
    columns,
    columnChecks,
    tableData: data,
    isLoading: loading,
    paginationState: pagination,
    searchState: searchParams,
    searchData: getDataByPage,
    resetSearch: resetSearchParams,
    onPageSizeChange: handleSizeChange,
    onCurrentPageChange: handleCurrentChange,
    refreshAll: refresh,
    refreshAfterCreate: refreshAfterAdd,
    refreshAfterUpdate: refreshAfterEdit
  } = useTable<UserListItem>({
    // 核心配置
    core: {
      apiFn: getUserList,
      apiParams: {
        current: 1,
        size: 20,
        name: '',        // 登录名
        role: '',        // 角色
        isActive: ''     // 状态
      },
      columnsFactory: () => [
        // { type: 'selection' }, // 勾选列
        {
          prop: 'id',
          width: 60,
          label: 'ID',
          align: 'center'
        },
        {
          prop: 'userName',
          label: '登陆名',
          width: 120,
          align: 'center',
          formatter: (row) => row.userName || '-'
        },
        {
          prop: 'realName',
          label: '姓名',
          width: 120,
          align: 'center',
          formatter: (row) => {
            // 会员显示真实姓名，其他角色显示用户名
            if (row.role === 'MEMBER' && row.memberProfile?.real_name) {
              return row.memberProfile.real_name
            }
            return row.userName || '-'
          }
        },
        {
          prop: 'userPhone',
          label: '电话',
          width: 120,
          align: 'center',
          formatter: (row) => row.userPhone || '-'
        },
        {
          prop: 'description',
          label: '说明',
          width: 120,
          align: 'center',
          formatter: (row) => {
            // 会员显示profile描述，助理显示assistant_profile描述，其他显示角色说明
            if (row.role === 'MEMBER' && row.memberProfile?.description) {
              return row.memberProfile.description
            }
            if (row.assistantProfile?.description) {
              return row.assistantProfile.description
            }
            return row.roleDisplay || '-'
          }
        },
        {
          prop: 'skuPrefix',
          label: 'SKU前缀',
          width: 100,
          align: 'center',
          formatter: (row) => {
            // 只有会员才有SKU前缀
            return row.memberProfile?.sku_prefix || '-'
          }
        },
        {
          prop: 'lastLoginInfo',
          label: '最后一次时间/IP',
          width: 140,
          align: 'center',
          formatter: (row) => {
            const time = row.lastLoginTime ? new Date(row.lastLoginTime).toLocaleString() : '-'
            const ip = row.lastLoginIP || '-'
            return h('div', { style: 'display: flex; flex-direction: column; align-items: center' }, [
              h('div', { style: 'margin: 0; font-size: 12px; line-height: 1.2' }, time),
              h('div', { style: 'margin: 0; font-size: 11px; color: #999; line-height: 1.2' }, ip)
            ])
          }
        },
        {
          prop: 'roleDisplay',
          label: '等级',
          width: 100,
          align: 'center',
          formatter: (row) => {
            return h(ElTag, { type: 'info', size: 'small' }, () => row.roleDisplay || '未知')
          }
        },
        {
          prop: 'isInternalMember',
          label: '内部会员',
          width: 90,
          align: 'center',
          formatter: (row) => {
            // 只有会员角色才有内部会员概念
            if (row.role === 'MEMBER') {
              const isInternal = row.memberProfile?.parent_account ? true : false
              return h(ElTag, {
                type: isInternal ? 'success' : 'info',
                size: 'small'
              }, () => isInternal ? '是' : '否')
            }
            return h(ElTag, {
              type: 'info',
              size: 'small'
            }, () => '无关')
          }
        },
        {
          prop: 'isWorkshopEmployee',
          label: '车间员工',
          width: 90,
          align: 'center',
          formatter: (row) => {
            const isWorkshop = row.role === 'OPERATION_SPECIALIST'
            return h(ElTag, {
              type: isWorkshop ? 'warning' : 'info',
              size: 'small'
            }, () => isWorkshop ? '是' : '否')
          }
        },
        {
          prop: 'crossBorder',
          label: '渠道',
          width: 110,
          align: 'center',
          formatter: (row) => {
            // 只有会员才有渠道信息
            if (row.role === 'MEMBER' && row.memberProfile) {
              const isCrossBorder = row.memberProfile.status === 'kjb'
              return h(ElTag, {
                type: isCrossBorder ? 'success' : 'info',
                size: 'small'
              }, () => isCrossBorder ? '跨境宝' : '支付宝及其他')
            }
            return h(ElTag, {
              type: 'info',
              size: 'small'
            }, () => '无')
          }
        },
        {
          prop: 'mwsSwitch',
          label: 'MWS状态',
          width: 90,
          align: 'center',
          formatter: (row) => {
            return h(ElSwitch, {
              modelValue: Boolean(row.mwsSwitch),
              'onUpdate:modelValue': (value: string | number | boolean) => handleMWSToggle(row, Boolean(value)),
              size: 'small'
            })
          }
        },
        {
          prop: 'isActive',
          label: '状态',
          width: 80,
          align: 'center',
          formatter: (row) => {
            return h(ElSwitch, {
              modelValue: Boolean(row.isActive),
              'onUpdate:modelValue': (value: string | number | boolean) => handleAccountToggle(row, Boolean(value)),
              size: 'small'
            })
          }
        },
        {
          prop: 'shippingAddress',
          label: '发货地址',
          width: 90,
          align: 'center',
          formatter: (row) => {
            // 只有会员才有发货地址
            if (row.role === 'MEMBER') {
              const addressText = row.memberProfile?.warehouse_location || ''
              return h('div', { style: 'display: flex; align-items: center; justify-content: center; gap: 8px;' }, [
                h('span', addressText),
                h(ArtButtonTable, {
                  type: 'edit',
                  size: 'small',
                  onClick: () => showAddressDialog(row)
                })
              ])
            }
            return '-'
          }
        },
        {
          prop: 'refresh_token',
          label: '令牌',
          width: 80,
          align: 'center',
          formatter: (row) => {
            if (row.hasRefreshToken) {
              return h(ArtButtonTable, {
                type: 'view',
                size: 'small',
                onClick: () => showTokenDialog(row)
              })
            } else {
              return h('span', { style: 'color: #999; font-size: 12px;' }, '无')
            }
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 75,
          align: 'center',
          fixed: 'right',
          formatter: (row) =>
            h('div', { style: 'display: flex; gap: 4px; justify-content: center;' }, [
              h(ArtButtonTable, {
                type: 'edit',
                size: 'small',
                onClick: () => showDialog('edit', row)
              }),
              h(ArtButtonTable, {
                type: 'permission',
                size: 'small',
                onClick: () => showPermissionDialog(row)
              })
            ])
        }
      ]
    },
    // 数据处理
    transform: {
      // 数据转换器 - 处理用户数据格式
      dataTransformer: (records: any) => {
        // 类型守卫检查
        if (!Array.isArray(records)) {
          console.warn('数据转换器: 期望数组类型，实际收到:', typeof records)
          return []
        }

        // 处理用户数据，确保字段完整性
        return records.map((item: any) => {
          return {
            ...item,
            // 确保isActive字段存在，默认为true
            isActive: item.isActive !== undefined ? item.isActive : true,
            // 确保区域信息存在
            regionName: item.regionName || '未知区域'
          }
        })
      }
    },
    // 性能优化
    performance: {
      enableCache: true, // 是否开启缓存
      cacheTime: 10 * 60 * 1000 // 缓存时间 10分钟
    },
    // 生命周期钩子
    hooks: {
      onError: (error) => ElMessage.error(error.message) // 错误处理
      // onSuccess: (data) => console.log('数据加载成功:', data), // 成功处理
      // onCacheHit: (data) => console.log('缓存命中:', data), // 缓存命中处理
      // resetFormCallback: () => console.log('重置表单')
    },
    // 调试配置
    debug: {
      enableLog: true // 是否开启日志
    }
  })

  /**
   * 显示用户弹窗
   */
  const showDialog = (type: Form.DialogType, row?: UserListItem): void => {
    console.log('打开弹窗:', { type, row })
    dialogType.value = type
    currentUserData.value = row || {}
    nextTick(() => {
      dialogVisible.value = true
    })
  }



  /**
   * 处理弹窗提交事件
   */
  const handleDialogSubmit = async () => {
    try {
      dialogVisible.value = false
      await (dialogType.value === 'add' ? refreshAfterAdd() : refreshAfterEdit())
      currentUserData.value = {}
    } catch (error) {
      console.error('提交失败:', error)
    }
  }

  /**
   * 显示权限管理弹窗
   */
  const showPermissionDialog = (row: UserListItem): void => {
    console.log('打开权限管理弹窗:', row)
    currentPermissionUserId.value = row.id
    nextTick(() => {
      permissionDialogVisible.value = true
    })
  }

  /**
   * 显示令牌展示弹窗
   */
  const showTokenDialog = (row: UserListItem): void => {
    console.log('打开令牌展示弹窗:', row)
    currentTokenUserId.value = row.id
    nextTick(() => {
      tokenDialogVisible.value = true
    })
  }

  /**
   * 显示地址编辑弹窗
   */
  const showAddressDialog = (row: UserListItem): void => {
    console.log('打开地址编辑弹窗:', row)
    currentAddressUserId.value = row.id

    // 解析现有的地址数据
    const warehouseLocation = row.memberProfile?.warehouse_location || ''
    // 这里可以根据实际的地址格式来解析
    // 暂时使用简单的格式
    currentAddressData.value = {
      name: row.memberProfile?.real_name || '',
      address1: warehouseLocation,
      address2: '',
      city: '',
      province: '',
      postalCode: '',
      country: 'CN'
    }

    nextTick(() => {
      addressDialogVisible.value = true
    })
  }

  /**
   * 处理地址提交
   */
  const handleAddressSubmit = async (addressData: any) => {
    try {
      console.log('提交地址数据:', addressData)

      // 将地址数据格式化为字符串存储到warehouse_location
      const addressString = `${addressData.name}, ${addressData.address1}${addressData.address2 ? ', ' + addressData.address2 : ''}, ${addressData.city}, ${addressData.province} ${addressData.postalCode}, ${addressData.country}`

      // 这里应该调用API更新用户的地址信息
      // 暂时先显示成功消息
      ElMessage.success('地址更新成功')

      // 刷新用户列表
      await refresh()

      // 关闭弹窗
      addressDialogVisible.value = false
    } catch (error: any) {
      console.error('地址更新失败:', error)
      ElMessage.error('地址更新失败')
    }
  }

  /**
   * 处理权限管理成功事件
   */
  const handlePermissionSuccess = async () => {
    try {
      await refreshAfterEdit()
      currentPermissionUserId.value = null
      ElMessage.success('权限管理操作成功')
    } catch (error) {
      console.error('权限管理操作失败:', error)
    }
  }

  /**
   * 处理表格行选择变化
   */
  const handleSelectionChange = (selection: UserListItem[]): void => {
    selectedRows.value = selection
    console.log('选中行数据:', selectedRows.value)
  }
</script>

<style lang="scss" scoped>
  .user-page {
    :deep(.user-name) {
      font-weight: 500;
      color: var(--art-text-gray-800);
    }

    :deep(.phone) {
      font-size: 12px;
      color: #999;
    }

    // 表格标题和内容居中对齐
    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-table__header {
          th {
            text-align: center;
            background-color: #f5f7fa !important;
            color: #606266;
            font-weight: 600;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          td {
            text-align: center;
          }
        }
      }

      // 斑马纹样式优化
      &.el-table--striped {
        .el-table__body tr.el-table__row--striped td {
          background-color: #fafafa;
        }
      }

      // 边框样式
      &.el-table--border {
        border: 1px solid #ebeef5;

        th, td {
          border-right: 1px solid #ebeef5;
        }

        th {
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
  }
</style>

import { useRealAPI } from '@/config/dataSource'
import { asyncRoutes } from '@/router/routes/asyncRoutes'
import { menuDataToRouter } from '@/router/utils/menuToRouter'
import { AppRouteRecord } from '@/types/router'
import request from '@/utils/http'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 菜单接口
export const menuService = {
  async getMenuList(delay = 300): Promise<MenuResponse> {
    try {
      // 检查是否使用真实API
      if (useRealAPI('menuSystem')) {
        console.log('🚀 使用真实菜单API')
        // 从后端获取动态菜单
        const response = await request.get<{ menuList: AppRouteRecord[] }>({
          url: '/api/v1/menu/list/'
        })

        console.log('✅ 菜单API调用成功:', response)
        // 处理后端返回的菜单数据
        const menuList = response.menuList.map((route) => menuDataToRouter(route))
        console.log('📋 处理后的菜单数据:', menuList)
        return { menuList }
      } else {
        console.log('📝 使用静态菜单配置')
        // 使用静态菜单配置（保持现有功能）
        const menuData = asyncRoutes
        // 处理菜单数据
        const menuList = menuData.map((route) => menuDataToRouter(route))
        // 模拟接口延迟
        await new Promise((resolve) => setTimeout(resolve, delay))
        return { menuList }
      }
    } catch (error) {
      // 如果真实API失败，回退到静态菜单
      console.error('❌ 动态菜单获取失败，回退到静态菜单:', error)
      const menuData = asyncRoutes
      const menuList = menuData.map((route) => menuDataToRouter(route))
      return { menuList }
    }
  }
}

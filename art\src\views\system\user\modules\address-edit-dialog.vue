<template>
  <ElDialog
    :model-value="visible"
    title="编辑发货地址"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="(value) => emit('update:visible', value)"
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="姓名" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入姓名" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="地址1" prop="address1">
            <ElInput v-model="formData.address1" placeholder="请输入地址1" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="地址2" prop="address2">
            <ElInput v-model="formData.address2" placeholder="请输入地址2（可选）" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="城市" prop="city">
            <ElInput v-model="formData.city" placeholder="请输入城市" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="省" prop="province">
            <ElInput v-model="formData.province" placeholder="请输入省份" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="邮政编码" prop="postalCode">
            <ElInput v-model="formData.postalCode" placeholder="请输入邮政编码" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="国家" prop="country">
            <ElSelect v-model="formData.country" placeholder="请选择国家" style="width: 100%">
              <ElOption value="CN" label="中国 (CN)" />
              <ElOption value="US" label="美国 (US)" />
              <ElOption value="UK" label="英国 (UK)" />
              <ElOption value="DE" label="德国 (DE)" />
              <ElOption value="JP" label="日本 (JP)" />
              <ElOption value="AU" label="澳大利亚 (AU)" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit" :loading="loading"> 提交 </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import {
    ElButton,
    ElCol,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElMessage,
    ElOption,
    ElRow,
    ElSelect
  } from 'element-plus'
  import { reactive, ref, watch } from 'vue'

  // 地址数据接口
  interface AddressData {
    name: string
    address1: string
    address2: string
    city: string
    province: string
    postalCode: string
    country: string
  }

  // 组件属性
  interface Props {
    visible: boolean
    addressData?: Partial<AddressData>
    userId?: number
  }

  // 组件事件
  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit', data: AddressData): void
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    addressData: () => ({}),
    userId: undefined
  })

  const emit = defineEmits<Emits>()

  // 表单引用
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const formData = reactive<AddressData>({
    name: '',
    address1: '',
    address2: '',
    city: '',
    province: '',
    postalCode: '',
    country: 'CN'
  })

  // 表单验证规则
  const rules: FormRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    address1: [{ required: true, message: '请输入地址1', trigger: 'blur' }],
    city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
    province: [{ required: true, message: '请输入省份', trigger: 'blur' }],
    postalCode: [{ required: true, message: '请输入邮政编码', trigger: 'blur' }],
    country: [{ required: true, message: '请选择国家', trigger: 'change' }]
  }

  // 监听地址数据变化，填充表单
  watch(
    () => props.addressData,
    (newData) => {
      if (newData) {
        Object.assign(formData, {
          name: newData.name || '',
          address1: newData.address1 || '',
          address2: newData.address2 || '',
          city: newData.city || '',
          province: newData.province || '',
          postalCode: newData.postalCode || '',
          country: newData.country || 'CN'
        })
      }
    },
    { immediate: true, deep: true }
  )

  // 关闭弹窗
  const handleClose = () => {
    emit('update:visible', false)
    // 重置表单
    formRef.value?.resetFields()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 发出提交事件
      emit('submit', { ...formData })

      ElMessage.success('地址保存成功')
      handleClose()
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }

  .el-form-item {
    margin-bottom: 20px;
  }
</style>

<template>
  <div class="menu-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model:filter="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    ></ArtSearchBar>

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton @click="showModel('menu', null, true)" v-ripple>
            添加菜单
          </ElButton>
          <ElButton @click="toggleExpand" v-ripple>
            {{ isExpanded ? '收起' : '展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>
      <!-- 表格 -->
      <ArtTable
        ref="tableRef"
        :loading="loading"
        :data="filteredTableData"
        :tableConfig="{
          rowKey: 'path',
          stripe: false
        }"
        :layout="{
          marginTop: 10
        }"
      >
        <template #default>
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col" />
        </template>
      </ArtTable>

      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="700px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="85px">
          <ElFormItem label="菜单类型">
            <ElRadioGroup v-model="labelPosition" :disabled="disableMenuType">
              <ElRadioButton value="menu" label="menu">菜单</ElRadioButton>
              <ElRadioButton value="button" label="button">权限</ElRadioButton>
            </ElRadioGroup>
          </ElFormItem>

          <template v-if="labelPosition === 'menu'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="菜单名称" prop="name">
                  <ElInput v-model="form.name" placeholder="菜单名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="菜单类型" prop="menuType">
                  <ElSelect v-model="form.menuType" placeholder="请选择菜单类型" style="width: 100%">
                    <ElOption label="目录" value="directory" />
                    <ElOption label="菜单" value="menu" />
                    <ElOption label="外链" value="link" />
                    <ElOption label="内嵌" value="iframe" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="路由地址" prop="path">
                  <ElInput
                    v-model="form.path"
                    placeholder="路由地址"
                    :disabled="form.menuType === 'directory'"
                  ></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="label">
                  <ElInput v-model="form.label" placeholder="权限标识"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="24">
                <ElFormItem label="图标" prop="icon">
                  <ArtIconSelector v-model="form.icon" :iconType="iconType" width="100%" />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="菜单排序" prop="sort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.sort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="外部链接" prop="link">
                  <ElInput
                    v-model="form.link"
                    placeholder="外部链接/内嵌地址(https://www.baidu.com)"
                  ></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="5">
                <ElFormItem label="是否启用" prop="isEnable">
                  <ElSwitch v-model="form.isEnable"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="页面缓存" prop="keepAlive">
                  <ElSwitch v-model="form.keepAlive"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="是否显示" prop="isHidden">
                  <ElSwitch v-model="form.isHidden"></ElSwitch>
                </ElFormItem>
              </ElCol>
              <ElCol :span="5">
                <ElFormItem label="是否内嵌" prop="isMenu">
                  <ElSwitch v-model="form.isIframe"></ElSwitch>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>

          <template v-if="labelPosition === 'button'">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限名称" prop="authName">
                  <ElInput v-model="form.authName" placeholder="权限名称"></ElInput>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="权限标识" prop="authLabel">
                  <ElInput v-model="form.authLabel" placeholder="权限标识"></ElInput>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="权限排序" prop="authSort" style="width: 100%">
                  <ElInputNumber
                    v-model="form.authSort"
                    style="width: 100%"
                    @change="handleChange"
                    :min="1"
                    controls-position="right"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </template>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm()">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
import { useAuth } from '@/composables/useAuth'
import { useTableColumns } from '@/composables/useTableColumns'
import { IconTypeEnum } from '@/enums/appEnum'
import { formatMenuTitle } from '@/router/utils/utils'
import { useMenuStore } from '@/store/modules/menu'
import { SearchFormItem } from '@/types'
import { AppRouteRecord } from '@/types/router'
import type { FormInstance, FormRules } from 'element-plus'
import { ElButton, ElMessage, ElMessageBox, ElPopover, ElTag } from 'element-plus'

  defineOptions({ name: 'Menus' })

  const { hasAuth } = useAuth()

  const menuStore = useMenuStore()
  const { menuList } = storeToRefs(menuStore)

  const loading = ref(false)

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    route: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 增加实际应用的搜索条件状态
  const appliedFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    // 将当前输入的筛选条件应用到实际搜索
    Object.assign(appliedFilters, { ...formFilters })
    getTableData()
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '菜单名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true
      }
    },
    {
      label: '路由地址',
      prop: 'route',
      type: 'input',
      config: {
        clearable: true
      }
    }
  ]

  // 构建菜单类型标签
  const buildMenuTypeTag = (row: AppRouteRecord) => {
    if (row.children && row.children.length > 0) {
      return 'info'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return 'success'
    } else if (row.path) {
      return 'primary'
    } else if (row.meta?.link) {
      return 'warning'
    }
  }

  // 构建菜单类型文本
  const buildMenuTypeText = (row: AppRouteRecord) => {
    if (row.children && row.children.length > 0) {
      return '目录'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return '内嵌'
    } else if (row.path) {
      return '菜单'
    } else if (row.meta?.link) {
      return '外链'
    }
  }

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      prop: 'meta.title',
      label: '菜单名称',
      minWidth: 120,
      formatter: (row: AppRouteRecord) => {
        return formatMenuTitle(row.meta?.title)
      }
    },
    {
      prop: 'type',
      label: '菜单类型',
      formatter: (row: AppRouteRecord) => {
        return h(ElTag, { type: buildMenuTypeTag(row) }, () => buildMenuTypeText(row))
      }
    },
    {
      prop: 'path',
      label: '路由',
      formatter: (row: AppRouteRecord) => {
        return row.meta?.link || row.path || ''
      }
    },
    {
      prop: 'meta.authList',
      label: '可操作权限',
      formatter: (row: AppRouteRecord) => {
        return h(
          'div',
          {},
          row.meta.authList?.map((item: { title: string; authMark: string }, index: number) => {
            return h(
              ElPopover,
              {
                placement: 'top-start',
                title: '操作',
                width: 200,
                trigger: 'click',
                key: index
              },
              {
                default: () =>
                  h('div', { style: 'margin: 0; text-align: right' }, [
                    h(
                      ElButton,
                      {
                        size: 'small',
                        type: 'primary',
                        onClick: () => showModel('button', item)
                      },
                      { default: () => '编辑' }
                    ),
                    h(
                      ElButton,
                      {
                        size: 'small',
                        type: 'danger',
                        onClick: () => deleteAuth()
                      },
                      { default: () => '删除' }
                    )
                  ]),
                reference: () => h(ElButton, { class: 'small-btn' }, { default: () => item.title })
              }
            )
          })
        )
      }
    },
    {
      prop: 'date',
      label: '编辑时间',
      formatter: () => '2022-3-12 12:00:00'
    },
    {
      prop: 'status',
      label: '隐藏菜单',
      formatter: (row) => {
        return h(ElTag, { type: row.meta.isHide ? 'danger' : 'info' }, () =>
          row.meta.isHide ? '是' : '否'
        )
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 180,
      formatter: (row: AppRouteRecord) => {
        return h('div', [
          h(ArtButtonTable, {
            type: 'add',
            onClick: () => showModel('menu', row, true)
          }),
          h(ArtButtonTable, {
            type: 'edit',
            onClick: () => showModel('menu', row, false)
          }),
          h(ArtButtonTable, {
            type: 'delete',
            onClick: () => deleteMenu(row)
          })
        ])
      }
    }
  ])

  const handleRefresh = () => {
    getTableData()
  }

  const dialogVisible = ref(false)
  const form = reactive({
    // 菜单
    name: '',
    path: '',
    label: '',
    icon: '',
    menuType: 'menu', // 默认为菜单类型
    isEnable: true,
    sort: 1,
    isMenu: true,
    keepAlive: true,
    isHidden: true,
    link: '',
    isIframe: false,
    // 权限 (修改这部分)
    authName: '',
    authLabel: '',
    authIcon: '',
    authSort: 1
  })
  const iconType = ref(IconTypeEnum.UNICODE)

  const labelPosition = ref('menu')
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    path: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    label: [{ required: true, message: '输入权限标识', trigger: 'blur' }],
    // 修改这部分
    authName: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
    authLabel: [{ required: true, message: '请输入权限权限标识', trigger: 'blur' }]
  })

  const tableData = ref<AppRouteRecord[]>([])

  onMounted(() => {
    getTableData()
  })

  const getTableData = () => {
    loading.value = true
    setTimeout(() => {
      // 先尝试从localStorage获取自定义菜单数据
      const customMenus = getCustomMenusFromStorage()
      if (customMenus && customMenus.length > 0) {
        tableData.value = customMenus
      } else {
        tableData.value = menuList.value
      }
      loading.value = false
    }, 500)
  }

  // 从localStorage获取自定义菜单数据
  const getCustomMenusFromStorage = () => {
    try {
      const stored = localStorage.getItem('customMenus')
      return stored ? JSON.parse(stored) : null
    } catch (error) {
      console.error('获取自定义菜单数据失败:', error)
      return null
    }
  }

  // 保存自定义菜单数据到localStorage
  const saveCustomMenusToStorage = (menus: AppRouteRecord[]) => {
    try {
      localStorage.setItem('customMenus', JSON.stringify(menus))
    } catch (error) {
      console.error('保存自定义菜单数据失败:', error)
    }
  }

  // 过滤后的表格数据
  const filteredTableData = computed(() => {
    // 深拷贝函数，避免修改原数据
    const deepClone = (obj: any): any => {
      if (obj === null || typeof obj !== 'object') return obj
      if (obj instanceof Date) return new Date(obj)
      if (Array.isArray(obj)) return obj.map((item) => deepClone(item))

      const cloned: any = {}
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          cloned[key] = deepClone(obj[key])
        }
      }
      return cloned
    }

    // 递归搜索函数
    const searchMenu = (items: AppRouteRecord[]): AppRouteRecord[] => {
      const results: AppRouteRecord[] = []

      for (const item of items) {
        // 获取搜索关键词，转换为小写并去除首尾空格
        const searchName = appliedFilters.name?.toLowerCase().trim() || ''
        const searchRoute = appliedFilters.route?.toLowerCase().trim() || ''

        // 获取菜单标题和路径，确保它们存在
        const menuTitle = formatMenuTitle(item.meta?.title || '').toLowerCase()
        const menuPath = (item.path || '').toLowerCase()

        // 使用 includes 进行模糊匹配
        const nameMatch = !searchName || menuTitle.includes(searchName)
        const routeMatch = !searchRoute || menuPath.includes(searchRoute)

        // 如果有子菜单，递归搜索
        if (item.children && item.children.length > 0) {
          const matchedChildren = searchMenu(item.children)
          // 如果子菜单有匹配项，保留当前菜单并更新子菜单
          if (matchedChildren.length > 0) {
            const clonedItem = deepClone(item)
            clonedItem.children = matchedChildren
            results.push(clonedItem)
            continue
          }
        }

        // 当前菜单匹配条件则返回
        if (nameMatch && routeMatch) {
          results.push(deepClone(item))
        }
      }

      return results
    }

    return searchMenu(tableData.value)
  })

  const isEdit = ref(false)
  const formRef = ref<FormInstance>()
  const currentEditingMenu = ref<AppRouteRecord | null>(null)
  const currentParentMenu = ref<AppRouteRecord | null>(null)
  const dialogTitle = computed(() => {
    const type = labelPosition.value === 'menu' ? '菜单' : '权限'
    return isEdit.value ? `编辑${type}` : `新建${type}`
  })

  const showDialog = (type: string, row: AppRouteRecord) => {
    showModel('menu', row, true)
  }

  const handleChange = () => {}

  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          // 如果是编辑现有菜单
          if (isEdit.value && currentEditingMenu.value) {
            // 更新菜单数据
            updateMenuData(currentEditingMenu.value, form)
            ElMessage.success('编辑成功')
          } else {
            // 添加新菜单
            if (currentParentMenu.value) {
              // 添加子菜单
              addSubMenu(currentParentMenu.value, form)
              ElMessage.success('添加成功')
            } else {
              // 添加顶级菜单
              addTopLevelMenu(form)
              ElMessage.success('添加成功')
            }
          }

          // 更新菜单列表
          getTableData()

          // 关闭对话框
          dialogVisible.value = false
        } catch (error) {
          console.error('保存菜单失败:', error)
          ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
        }
      }
    })
  }

  const showModel = (type: string, row?: any, lock: boolean = false) => {
    dialogVisible.value = true
    labelPosition.value = type
    isEdit.value = false
    lockMenuType.value = lock
    resetForm()

    // 重置当前编辑菜单和父菜单
    currentEditingMenu.value = null
    currentParentMenu.value = null

    if (row) {
      if (lock) {
        // 如果是添加子菜单，设置父菜单
        currentParentMenu.value = row
      } else {
        // 如果是编辑菜单，设置当前编辑菜单
        isEdit.value = true
        currentEditingMenu.value = row
      }

      nextTick(() => {
        // 回显数据
        if (type === 'menu') {
          // 菜单数据回显
          form.name = formatMenuTitle(row.meta?.title || '')
          form.path = row.path || ''
          form.label = row.name || ''
          form.icon = row.meta?.icon || ''

          // 设置菜单类型
          if (row.children && row.children.length > 0) {
            form.menuType = 'directory'
          } else if (row.meta?.link && row.meta?.isIframe) {
            form.menuType = 'iframe'
          } else if (row.meta?.link) {
            form.menuType = 'link'
          } else {
            form.menuType = 'menu'
          }

          form.sort = row.meta?.sort || 1
          form.isMenu = row.meta?.isMenu !== false
          form.keepAlive = row.meta?.keepAlive !== false
          form.isHidden = row.meta?.isHidden || false
          form.isEnable = row.meta?.isEnable !== false
          form.link = row.meta?.link || ''
          form.isIframe = row.meta?.isIframe || false
        } else {
          // 权限按钮数据回显
          form.authName = row.title || ''
          form.authLabel = row.authMark || ''
          form.authIcon = row.icon || ''
          form.authSort = row.sort || 1
        }
      })
    }
  }

  const resetForm = () => {
    formRef.value?.resetFields()
    Object.assign(form, {
      // 菜单
      name: '',
      path: '',
      label: '',
      icon: '',
      menuType: 'menu',
      sort: 1,
      isMenu: true,
      keepAlive: true,
      isHidden: true,
      link: '',
      isIframe: false,
      // 权限
      authName: '',
      authLabel: '',
      authIcon: '',
      authSort: 1
    })
  }

  const deleteMenu = async (row: AppRouteRecord) => {
    try {
      await ElMessageBox.confirm(`确定要删除菜单"${String(row.meta?.title || row.name)}"吗？删除后无法恢复`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 删除菜单
      const removeMenu = (menus: AppRouteRecord[], targetMenu: AppRouteRecord): boolean => {
        // 在当前层级查找并删除
        const index = menus.findIndex(menu => menu.name === targetMenu.name && menu.path === targetMenu.path)
        if (index !== -1) {
          menus.splice(index, 1)
          return true
        }

        // 递归查找子菜单
        for (const menu of menus) {
          if (menu.children && menu.children.length > 0) {
            if (removeMenu(menu.children, targetMenu)) {
              return true
            }
          }
        }

        return false
      }

      // 复制当前菜单列表
      const updatedMenus = [...menuList.value]

      // 执行删除操作
      if (removeMenu(updatedMenus, row)) {
        // 更新菜单store
        menuStore.setMenuList(updatedMenus)

        // 保存到localStorage
        saveCustomMenusToStorage(updatedMenus)

        // 刷新表格数据
        getTableData()

        ElMessage.success('删除成功')
      } else {
        ElMessage.error('未找到要删除的菜单')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  const deleteAuth = async () => {
    try {
      await ElMessageBox.confirm('确定要删除该权限吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      ElMessage.success('删除成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 修改计算属性，增加锁定控制参数
  const disableMenuType = computed(() => {
    // 编辑权限时锁定为权限类型
    if (isEdit.value && labelPosition.value === 'button') return true
    // 编辑菜单时锁定为菜单类型
    if (isEdit.value && labelPosition.value === 'menu') return true
    // 顶部添加菜单按钮时锁定为菜单类型
    if (!isEdit.value && labelPosition.value === 'menu' && lockMenuType.value) return true
    return false
  })

  // 添加一个控制变量
  const lockMenuType = ref(false)

  const isExpanded = ref(false)
  const tableRef = ref()

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value[isExpanded.value ? 'expandAll' : 'collapseAll']()
      }
    })
  }

  // 更新菜单数据
  const updateMenuData = (targetMenu: AppRouteRecord, formData: any) => {
    // 更新菜单的meta信息
    if (targetMenu.meta) {
      targetMenu.meta.title = formData.name
      targetMenu.meta.icon = formData.icon
      targetMenu.meta.sort = formData.sort
      targetMenu.meta.isMenu = formData.isMenu
      targetMenu.meta.keepAlive = formData.keepAlive
      targetMenu.meta.isHidden = formData.isHidden
      targetMenu.meta.isEnable = formData.isEnable

      // 根据菜单类型设置相关属性
      if (formData.menuType === 'link' || formData.menuType === 'iframe') {
        targetMenu.meta.link = formData.link || ''
        targetMenu.meta.isIframe = formData.menuType === 'iframe'
      } else {
        targetMenu.meta.link = ''
        targetMenu.meta.isIframe = false
      }
    }

    // 更新路由信息
    if (formData.menuType !== 'directory') {
      targetMenu.path = formData.path
    }
    targetMenu.name = formData.label

    // 如果是目录类型，确保有children数组
    if (formData.menuType === 'directory' && !targetMenu.children) {
      targetMenu.children = []
    }

    // 更新菜单store
    const updatedMenus = [...menuList.value]
    menuStore.setMenuList(updatedMenus)

    // 保存到localStorage
    saveCustomMenusToStorage(updatedMenus)
  }

  // 添加子菜单
  const addSubMenu = (parentMenu: AppRouteRecord, formData: any) => {
    const newMenu: AppRouteRecord = {
      name: formData.label,
      path: formData.menuType === 'directory' ? '' : formData.path,
      component: '/index/index',
      meta: {
        title: formData.name,
        icon: formData.icon,
        sort: formData.sort,
        isMenu: formData.isMenu,
        keepAlive: formData.keepAlive,
        isHidden: formData.isHidden,
        isEnable: formData.isEnable,
        link: (formData.menuType === 'link' || formData.menuType === 'iframe') ? formData.link : '',
        isIframe: formData.menuType === 'iframe'
      }
    }

    // 如果是目录类型，添加children数组
    if (formData.menuType === 'directory') {
      newMenu.children = []
    }

    if (!parentMenu.children) {
      parentMenu.children = []
    }
    parentMenu.children.push(newMenu)

    // 更新菜单store
    const updatedMenus = [...menuList.value]
    menuStore.setMenuList(updatedMenus)

    // 保存到localStorage
    saveCustomMenusToStorage(updatedMenus)
  }

  // 添加顶级菜单
  const addTopLevelMenu = (formData: any) => {
    const newMenu: AppRouteRecord = {
      name: formData.label,
      path: formData.menuType === 'directory' ? '' : formData.path,
      component: '/index/index',
      meta: {
        title: formData.name,
        icon: formData.icon,
        sort: formData.sort,
        isMenu: formData.isMenu,
        keepAlive: formData.keepAlive,
        isHidden: formData.isHidden,
        isEnable: formData.isEnable,
        link: (formData.menuType === 'link' || formData.menuType === 'iframe') ? formData.link : '',
        isIframe: formData.menuType === 'iframe'
      }
    }

    // 如果是目录类型，添加children数组
    if (formData.menuType === 'directory') {
      newMenu.children = []
    }

    const newMenuList = [...menuList.value, newMenu]
    menuStore.setMenuList(newMenuList)

    // 保存到localStorage
    saveCustomMenusToStorage(newMenuList)
  }
</script>

<style lang="scss" scoped>
  .menu-page {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }
</style>

<!-- 客户管理页面 -->
<template>
  <div class="customer-page art-full-height">
    <!-- 搜索栏 -->
    <ElCard shadow="never" style="margin-bottom: 16px">
      <ElForm :model="searchForm" inline>
        <ElFormItem label="客户名称">
          <ElInput v-model="searchForm.name" placeholder="请输入客户名称" clearable />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <div
        style="
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <ElButton type="primary" @click="handleAdd">新增客户</ElButton>
          <ElButton @click="handleCreateTestData">创建测试数据</ElButton>
        </div>
        <div>
          <ElButton @click="handleRefresh">刷新</ElButton>
        </div>
      </div>

      <!-- 表格 -->
      <ElTable :data="tableData" :loading="loading" border>
        <ElTableColumn type="index" label="序号" width="60" />
        <ElTableColumn prop="customer_code" label="客户编码" width="120" />
        <ElTableColumn prop="name" label="客户名称" min-width="150" />
        <ElTableColumn prop="contact_person" label="联系人" width="100" />
        <ElTableColumn prop="phone" label="联系电话" width="120" />
        <ElTableColumn prop="email" label="邮箱" width="180" />
        <ElTableColumn prop="address" label="地址" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="created_at" label="创建时间" width="160" />
        <ElTableColumn label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '正常' : '禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <ElButton type="primary" size="small" @click="handleEdit(row)">编辑</ElButton>
            <ElButton type="danger" size="small" @click="handleDelete(row)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div style="margin-top: 16px; display: flex; justify-content: center">
        <ElPagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { CustomerService, TestDataService } from '@/api/businessApi'
  import { ElMessage, ElMessageBox } from 'element-plus'

  defineOptions({ name: 'BusinessCustomer' })

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<Api.Business.CustomerListItem[]>([])

  // 搜索表单
  const searchForm = reactive({
    name: ''
  })

  // 分页数据
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  })

  // 获取客户列表
  const getCustomerList = async () => {
    try {
      loading.value = true
      const params = {
        current: pagination.current,
        size: pagination.size,
        name: searchForm.name
      }

      const response = await CustomerService.getCustomerList(params)
      tableData.value = response.records
      pagination.total = response.total
    } catch (error) {
      ElMessage.error('获取客户列表失败')
      console.error('获取客户列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 搜索
  const handleSearch = () => {
    pagination.current = 1
    getCustomerList()
  }

  // 重置
  const handleReset = () => {
    searchForm.name = ''
    pagination.current = 1
    getCustomerList()
  }

  // 刷新
  const handleRefresh = () => {
    getCustomerList()
  }

  // 分页大小改变
  const handleSizeChange = (size: number) => {
    pagination.size = size
    pagination.current = 1
    getCustomerList()
  }

  // 当前页改变
  const handleCurrentChange = (current: number) => {
    pagination.current = current
    getCustomerList()
  }

  // 新增客户
  const handleAdd = () => {
    ElMessage.info('新增客户功能开发中...')
  }

  // 编辑客户
  const handleEdit = (row: Api.Business.CustomerListItem) => {
    ElMessage.info(`编辑客户: ${row.name}`)
  }

  // 删除客户
  const handleDelete = (row: Api.Business.CustomerListItem) => {
    ElMessageBox.confirm(`确定要删除客户 "${row.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.info('删除功能开发中...')
    })
  }

  // 创建测试数据
  const handleCreateTestData = async () => {
    try {
      loading.value = true
      await TestDataService.createTestData()
      ElMessage.success('测试数据创建成功')
      getCustomerList()
    } catch (error) {
      ElMessage.error('创建测试数据失败')
      console.error('创建测试数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 初始化
  onMounted(() => {
    getCustomerList()
  })
</script>

<style lang="scss" scoped>
  .customer-page {
    padding: 16px;
  }
</style>
